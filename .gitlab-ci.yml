image: 
  name: gradle:jdk24-alpine
  pull_policy: always

variables:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle"

stages:
  - build
  - test
  - deploy

before_script:
  - export GRADLE_USER_HOME
  - export YEAR_WEEK=$(date +%Y.%V)
  - export AVER="${YEAR_WEEK}.${CI_PIPELINE_ID}"
  - echo "Variable AVER set to:$AVER"

build:
  stage: build
  script:
    - echo "Building CDTest application..."
    - gradle --build-cache clean bootJar
    - echo "Build completed successfully"
    - echo "Generated JAR files:"
    - ls -la build/libs/
    - ARTIFACT_NAME="cdtest-${AVER}.jar"
    - echo "Renaming JAR to:$ARTIFACT_NAME"
    - ORIGINAL_JAR=$(find build/libs -name "*.jar" -type f | head -1)
    - mv "$ORIGINAL_JAR" "build/libs/$ARTIFACT_NAME"
    - echo "Final artifact:"
    - ls -la build/libs/
  cache:
    key: "$CI_COMMIT_REF_NAME"
    policy: push
    paths:
      - build
      - .gradle
  artifacts:
    name: "cdtest-${AVER}"
    paths:
      - build/libs/${ARTIFACT_NAME}
    reports:
      dotenv: build.env
    expire_in: 30 days
  after_script:
    - echo "JAR_FILE=build/libs/$ARTIFACT_NAME" > build.env
    - echo "JAR_NAME=$ARTIFACT_NAME" >> build.env
    - echo "AVER=$AVER" >> build.env

test:
  stage: test
  script:
    - echo "Running tests..."
    - gradle check
    - echo "Tests completed successfully"
  cache:
    key: "$CI_COMMIT_REF_NAME"
    policy: pull
    paths:
      - build
      - .gradle
  artifacts:
    when: always
    reports:
      junit:
        - build/test-results/test/TEST-*.xml
    paths:
      - build/reports/tests/test/
    expire_in: 7 days
  coverage: '/Total.*?([0-9]{1,3})%/'

deploy:
  stage: deploy
  dependencies:
    - build
  script:
    - |
      echo "Deploying CDTest application..."
      echo "Using JAR file:$JAR_NAME"
      echo "=== Deployment Information ==="
      echo "Commit: $CI_COMMIT_SHA"
      echo "Branch: $CI_COMMIT_REF_NAME"
      echo "JAR File: $JAR_NAME"
      echo "File Size: $(du -h $JAR_FILE | cut -f1)"
  environment:
    name: production
    url: https://cdtest.example.com
  when: manual
  only:
    - main
