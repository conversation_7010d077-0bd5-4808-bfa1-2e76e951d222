#!/bin/bash

# Local build script for CDTest application
# This script mimics the CI/CD build process locally

set -e

echo "🚀 Starting local build for CDTest application..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
./gradlew clean

# Build the application
echo "🔨 Building application..."
./gradlew bootJar

# Rename JAR file to match CI/CD naming convention
echo "📝 Renaming JAR file to match CI/CD convention..."
WEEK_NUMBER=$(date +%V)
BUILD_NUMBER_LOCAL=999  # Use 999 for local builds
ARTIFACT_NAME="cdtest-${WEEK_NUMBER}.${BUILD_NUMBER_LOCAL}.jar"

# Find the generated JAR and rename it
ORIGINAL_JAR=$(find build/libs -name "*.jar" -type f | head -1)
if [ -n "$ORIGINAL_JAR" ]; then
    mv "$ORIGINAL_JAR" "build/libs/$ARTIFACT_NAME"
    echo "Renamed to: $ARTIFACT_NAME"
fi

# Display build results
echo "✅ Build completed successfully!"
echo ""
echo "📦 Generated artifacts:"
ls -la build/libs/

# Get JAR file info
JAR_FILE="build/libs/$ARTIFACT_NAME"
JAR_NAME="$ARTIFACT_NAME"
JAR_SIZE=$(du -h "$JAR_FILE" | cut -f1)

echo ""
echo "=== Artifact Information ==="
echo "JAR File: $JAR_NAME"
echo "File Size: $JAR_SIZE"
echo "Full Path: $JAR_FILE"
echo "=========================="

# Test the JAR file
echo ""
echo "🧪 Testing JAR file..."
if java -jar "$JAR_FILE" --help > /dev/null 2>&1; then
    echo "✅ JAR file is executable"
else
    echo "⚠️  JAR file test completed (Spring Boot apps don't support --help)"
fi

echo ""
echo "🎉 Local build completed successfully!"
echo "📁 JAR file location: $JAR_FILE"
echo ""
echo "To run the application locally:"
echo "  java -jar $JAR_FILE"
echo ""
echo "To run with Docker:"
echo "  docker build -t cdtest ."
echo "  docker run -p 8080:8080 cdtest"
